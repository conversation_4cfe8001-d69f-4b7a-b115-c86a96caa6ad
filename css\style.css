/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Colors */
    --primary-color: #2c5aa0;
    --secondary-color: #e74c3c;
    --accent-color: #27ae60;
    --dark-color: #2c3e50;
    --light-color: #ecf0f1;
    --white: #ffffff;
    --gray-light: #bdc3c7;
    --gray-dark: #7f8c8d;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #2c5aa0 0%, #3498db 100%);
    --gradient-secondary: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    --gradient-hero: linear-gradient(135deg, rgba(44, 90, 160, 0.9) 0%, rgba(52, 152, 219, 0.8) 100%);

    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 2rem;
    --spacing-lg: 3rem;
    --spacing-xl: 4rem;

    /* Typography */
    --font-family: 'Cairo', sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 2rem;
    --font-size-4xl: 2.5rem;

    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15);

    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--dark-color);
    background-color: var(--white);
    overflow-x: hidden;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-sm);
    z-index: 1000;
    transition: var(--transition-normal);
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-md);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
    text-decoration: none;
}

.nav-logo i {
    margin-left: var(--spacing-xs);
    color: var(--secondary-color);
}

.nav-menu {
    display: flex;
    list-style: none;
    align-items: center;
    gap: var(--spacing-md);
}

.nav-link {
    text-decoration: none;
    color: var(--dark-color);
    font-weight: 500;
    transition: var(--transition-fast);
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: var(--transition-fast);
}

.nav-link:hover::after {
    width: 100%;
}

.lang-toggle {
    background: var(--gradient-primary);
    color: var(--white);
    border: none;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 20px;
    cursor: pointer;
    font-size: var(--font-size-sm);
    transition: var(--transition-fast);
}

.lang-toggle:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--dark-color);
    margin: 3px 0;
    transition: var(--transition-fast);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    background: var(--gradient-hero);
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    align-items: center;
    position: relative;
    z-index: 2;
}

.hero-content {
    color: var(--white);
}

.hero-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    line-height: 1.2;
}

.highlight {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-lg);
    opacity: 0.9;
    line-height: 1.8;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: 30px;
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition-normal);
    border: 2px solid transparent;
}

.btn-primary {
    background: var(--white);
    color: var(--primary-color);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: transparent;
    color: var(--white);
    border-color: var(--white);
}

.btn-secondary:hover {
    background: var(--white);
    color: var(--primary-color);
}

.hero-stats {
    display: flex;
    gap: var(--spacing-lg);
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--white);
}

.stat-label {
    font-size: var(--font-size-sm);
    opacity: 0.8;
}

.hero-visual {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.ecg-animation {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: var(--spacing-md);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

#ecgCanvas {
    max-width: 100%;
    height: auto;
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.floating-icon {
    position: absolute;
    color: rgba(255, 255, 255, 0.6);
    font-size: var(--font-size-2xl);
    animation: float 3s ease-in-out infinite;
    animation-delay: var(--delay);
}

.floating-icon:nth-child(1) { top: 20%; right: 10%; }
.floating-icon:nth-child(2) { top: 60%; left: 15%; }
.floating-icon:nth-child(3) { bottom: 20%; right: 20%; }

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.hero-scroll {
    position: absolute;
    bottom: var(--spacing-md);
    left: 50%;
    transform: translateX(-50%);
    color: var(--white);
    font-size: var(--font-size-xl);
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
    40% { transform: translateX(-50%) translateY(-10px); }
    60% { transform: translateX(-50%) translateY(-5px); }
}

/* Section Styles */
.section-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.section-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: var(--spacing-sm);
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--gray-dark);
    max-width: 600px;
    margin: 0 auto;
}

/* About Section */
.about {
    padding: var(--spacing-xl) 0;
    background: var(--light-color);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    align-items: center;
}

.about-text h3 {
    font-size: var(--font-size-2xl);
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.about-text p {
    font-size: var(--font-size-lg);
    line-height: 1.8;
    margin-bottom: var(--spacing-lg);
    color: var(--gray-dark);
}

.features-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
}

.feature-item {
    background: var(--white);
    padding: var(--spacing-md);
    border-radius: 15px;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
}

.feature-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.feature-item i {
    font-size: var(--font-size-2xl);
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.feature-item h4 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xs);
    color: var(--dark-color);
}

.feature-item p {
    font-size: var(--font-size-sm);
    color: var(--gray-dark);
    line-height: 1.6;
}

/* System Diagram */
.system-diagram {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: var(--white);
    border-radius: 20px;
    box-shadow: var(--shadow-lg);
}

.diagram-node {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--font-size-xl);
    position: relative;
    cursor: pointer;
    transition: var(--transition-normal);
}

.diagram-node:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-lg);
}

.diagram-connection {
    width: 40px;
    height: 2px;
    background: var(--primary-color);
    position: relative;
}

.diagram-connection::after {
    content: '';
    position: absolute;
    right: -5px;
    top: -3px;
    width: 0;
    height: 0;
    border-left: 8px solid var(--primary-color);
    border-top: 4px solid transparent;
    border-bottom: 4px solid transparent;
}

/* Features Section */
.features {
    padding: var(--spacing-xl) 0;
    background: var(--white);
}

.features-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-lg);
}

.feature-card {
    background: var(--white);
    padding: var(--spacing-lg);
    border-radius: 20px;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    border: 1px solid var(--light-color);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-md);
}

.feature-icon i {
    font-size: var(--font-size-2xl);
    color: var(--white);
}

.feature-card h3 {
    font-size: var(--font-size-xl);
    color: var(--dark-color);
    margin-bottom: var(--spacing-sm);
}

.feature-card p {
    color: var(--gray-dark);
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
}

.feature-card ul {
    list-style: none;
}

.feature-card li {
    padding: var(--spacing-xs) 0;
    color: var(--gray-dark);
    position: relative;
    padding-right: var(--spacing-md);
}

.feature-card li::before {
    content: '✓';
    position: absolute;
    right: 0;
    color: var(--accent-color);
    font-weight: bold;
}

/* Loading */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-normal);
}

.loading.show {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--light-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-md);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        right: -100%;
        top: 70px;
        flex-direction: column;
        background: var(--white);
        width: 100%;
        text-align: center;
        transition: var(--transition-normal);
        box-shadow: var(--shadow-lg);
        padding: var(--spacing-md) 0;
    }

    .nav-menu.active {
        right: 0;
    }

    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-title {
        font-size: var(--font-size-3xl);
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .about-content {
        grid-template-columns: 1fr;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .system-diagram {
        flex-direction: column;
    }

    .diagram-connection {
        width: 2px;
        height: 40px;
        transform: rotate(90deg);
    }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Diagrams Section */
.diagrams {
    padding: var(--spacing-xl) 0;
    background: var(--light-color);
}

.diagrams-container {
    margin-top: var(--spacing-xl);
}

.diagram-category {
    margin-bottom: var(--spacing-xl);
}

.diagram-category h3 {
    font-size: var(--font-size-2xl);
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
    text-align: center;
}

.diagram-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.diagram-item {
    position: relative;
    background: var(--white);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    cursor: pointer;
}

.diagram-item:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.diagram-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: var(--transition-normal);
}

.diagram-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: var(--white);
    padding: var(--spacing-lg);
    transform: translateY(100%);
    transition: var(--transition-normal);
}

.diagram-item:hover .diagram-overlay {
    transform: translateY(0);
}

.diagram-overlay h4 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xs);
}

.diagram-overlay p {
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-md);
    opacity: 0.9;
}

.btn-view {
    background: var(--white);
    color: var(--primary-color);
    border: none;
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: 20px;
    font-size: var(--font-size-sm);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
}

.btn-view:hover {
    background: var(--primary-color);
    color: var(--white);
}

/* Applications Section */
.applications {
    padding: var(--spacing-xl) 0;
    background: var(--white);
}

.applications-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
}

.application-card {
    background: var(--white);
    padding: var(--spacing-lg);
    border-radius: 20px;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--light-color);
    transition: var(--transition-normal);
    text-align: center;
}

.application-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.app-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-md);
}

.app-icon i {
    font-size: var(--font-size-2xl);
    color: var(--white);
}

.application-card h3 {
    font-size: var(--font-size-xl);
    color: var(--dark-color);
    margin-bottom: var(--spacing-sm);
}

.application-card p {
    color: var(--gray-dark);
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
}

.app-features {
    list-style: none;
    text-align: right;
}

.app-features li {
    padding: var(--spacing-xs) 0;
    color: var(--gray-dark);
    position: relative;
    padding-right: var(--spacing-md);
}

.app-features li::before {
    content: '✓';
    position: absolute;
    right: 0;
    color: var(--accent-color);
    font-weight: bold;
}

/* Contact Section */
.contact {
    padding: var(--spacing-xl) 0;
    background: var(--light-color);
}

.contact-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    align-items: start;
}

.contact-info h3,
.contact-form h3 {
    font-size: var(--font-size-2xl);
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.contact-item i {
    font-size: var(--font-size-xl);
    color: var(--primary-color);
    margin-top: var(--spacing-xs);
}

.contact-item h4 {
    font-size: var(--font-size-lg);
    color: var(--dark-color);
    margin-bottom: var(--spacing-xs);
}

.contact-item p {
    color: var(--gray-dark);
    line-height: 1.6;
}

.social-links {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

.social-link {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    text-decoration: none;
    transition: var(--transition-normal);
}

.social-link:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.social-link i {
    font-size: var(--font-size-lg);
}

/* Contact Form */
.contact-form {
    background: var(--white);
    padding: var(--spacing-lg);
    border-radius: 20px;
    box-shadow: var(--shadow-md);
}

.form-group {
    margin-bottom: var(--spacing-md);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid var(--light-color);
    border-radius: 10px;
    font-size: var(--font-size-base);
    font-family: var(--font-family);
    transition: var(--transition-fast);
    background: var(--white);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* Footer */
.footer {
    background: var(--dark-color);
    color: var(--white);
    padding: var(--spacing-xl) 0 var(--spacing-md);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.footer-section h4 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-md);
    color: var(--white);
}

.footer-section p {
    color: var(--gray-light);
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: var(--spacing-xs);
}

.footer-section ul li a {
    color: var(--gray-light);
    text-decoration: none;
    transition: var(--transition-fast);
}

.footer-section ul li a:hover {
    color: var(--white);
}

.footer-logo {
    display: flex;
    align-items: center;
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--white);
    margin-bottom: var(--spacing-md);
}

.footer-logo i {
    margin-left: var(--spacing-xs);
    color: var(--secondary-color);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: var(--spacing-md);
    text-align: center;
    color: var(--gray-light);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: var(--white);
    margin: 5% auto;
    padding: 0;
    border-radius: 20px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow: hidden;
    position: relative;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    background: var(--gradient-primary);
    color: var(--white);
    padding: var(--spacing-md) var(--spacing-lg);
}

.modal-header h2 {
    margin: 0;
    font-size: var(--font-size-2xl);
}

.modal-body {
    padding: var(--spacing-lg);
    max-height: 70vh;
    overflow-y: auto;
}

.modal-body img {
    width: 100%;
    height: auto;
    border-radius: 10px;
    margin-bottom: var(--spacing-md);
}

.modal-description p {
    color: var(--gray-dark);
    line-height: 1.6;
    font-size: var(--font-size-lg);
}

.close {
    position: absolute;
    top: var(--spacing-md);
    left: var(--spacing-md);
    color: var(--white);
    font-size: var(--font-size-2xl);
    font-weight: bold;
    cursor: pointer;
    z-index: 1;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    transition: var(--transition-fast);
}

.close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* Tooltip */
[data-tooltip] {
    position: relative;
}

[data-tooltip]:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--dark-color);
    color: var(--white);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 5px;
    font-size: var(--font-size-xs);
    white-space: nowrap;
    z-index: 1000;
}
