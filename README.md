# ECG System Design - Landing Page

A comprehensive web-based landing page for an ECG (Electrocardiogram) System Design project focused on biomedical engineering education and signal acquisition.

## 🚀 Features

### 🎨 Modern Design
- **Responsive Layout**: Fully responsive design that works on all devices
- **Arabic/English Support**: Bilingual interface with RTL/LTR support
- **Smooth Animations**: CSS animations and transitions for enhanced user experience
- **Interactive Elements**: Hover effects, modals, and dynamic content

### 📊 Technical Sections
- **Hero Section**: Animated ECG waveform with project introduction
- **About System**: Overview of biomedical engineering principles
- **Features**: Key capabilities of the ECG system
- **Technical Diagrams**: Interactive gallery of circuit diagrams and block diagrams
- **Applications**: Real-world use cases and applications
- **Contact Form**: Functional contact form with validation

### 🔧 Interactive Components
- **ECG Animation**: Real-time ECG waveform simulation using HTML5 Canvas
- **Diagram Modal**: Click-to-view detailed technical diagrams
- **Language Toggle**: Switch between Arabic and English
- **Smooth Scrolling**: Navigation with smooth scroll effects
- **Loading Screen**: Professional loading animation

## 📁 Project Structure

```
ECG System Design/
├── index.html              # Main HTML file
├── css/
│   └── style.css           # Main stylesheet
├── js/
│   └── script.js           # JavaScript functionality
├── assets/                 # Images and assets folder
├── *.png                   # Technical diagrams and circuits
└── README.md              # Project documentation
```

## 🛠️ Technologies Used

- **HTML5**: Semantic markup and Canvas API
- **CSS3**: Modern styling with CSS Grid, Flexbox, and animations
- **JavaScript (ES6+)**: Interactive functionality and DOM manipulation
- **Font Awesome**: Icons and visual elements
- **Google Fonts**: Cairo font for Arabic text support

## 📋 Technical Diagrams Included

### Block Diagrams
- Instrumentation Amplifier
- Digital Filter
- Arduino Data Acquisition
- Real-time System
- Remote Patient Monitoring

### Circuit Diagrams
- Driven Right Leg Circuit
- Inverting Amplifier
- Non-inverting Amplifier
- Voltage Follower
- R-2R DAC
- LM35 Temperature Sensor with Arduino

## 🚀 Getting Started

1. **Clone or Download** the project files
2. **Open** `index.html` in a modern web browser
3. **Ensure** all image files are in the same directory as the HTML file
4. **Navigate** through the sections using the navigation menu

## 📱 Browser Compatibility

- ✅ Chrome (recommended)
- ✅ Firefox
- ✅ Safari
- ✅ Edge
- ✅ Mobile browsers

## 🎯 Key Features Explained

### ECG Waveform Animation
The landing page features a real-time ECG waveform simulation that demonstrates:
- P, QRS, and T wave components
- Realistic heart rate simulation (75 BPM)
- Grid background for medical chart appearance
- Smooth animation using requestAnimationFrame

### Interactive Diagram Gallery
- **Hover Effects**: Overlay information on hover
- **Modal View**: Click to view full-size diagrams with descriptions
- **Categorized Display**: Organized by block diagrams and circuits
- **Responsive Grid**: Adapts to different screen sizes

### Bilingual Support
- **Arabic (Default)**: Right-to-left layout with Arabic content
- **English Toggle**: Switch to left-to-right English interface
- **Font Support**: Optimized fonts for both languages

## 🔧 Customization

### Colors
The CSS uses CSS custom properties (variables) for easy color customization:
```css
:root {
    --primary-color: #2c5aa0;
    --secondary-color: #e74c3c;
    --accent-color: #27ae60;
    /* ... more variables */
}
```

### Content
- Edit `index.html` to modify text content
- Update diagram descriptions in `js/script.js`
- Replace images with your own technical diagrams

### Styling
- Modify `css/style.css` for visual changes
- Responsive breakpoints are defined for mobile optimization
- Animation timings and effects can be adjusted

## 📊 Performance Features

- **Optimized Images**: Proper image sizing and compression
- **Efficient Animations**: Hardware-accelerated CSS animations
- **Lazy Loading**: Intersection Observer for performance
- **Minimal Dependencies**: Lightweight external resources

## 🎓 Educational Content

This landing page is designed for:
- **Biomedical Engineering Students**: Learning ECG system design
- **Medical Device Engineers**: Understanding signal acquisition
- **Researchers**: Exploring biomedical signal processing
- **Educators**: Teaching biomedical engineering concepts

## 📞 Contact Information

The contact section includes:
- Email: <EMAIL>
- Phone: +966 11 123 4567
- Address: Biomedical Engineering Department, King Saud University, Riyadh
- Social media links (LinkedIn, Twitter, GitHub, YouTube)

## 🔄 Future Enhancements

Potential improvements for the landing page:
- [ ] Add more interactive ECG analysis tools
- [ ] Implement real data visualization
- [ ] Add video tutorials section
- [ ] Create downloadable resources
- [ ] Integrate with learning management systems
- [ ] Add user authentication for advanced features

## 📝 License

This project is created for educational purposes in biomedical engineering.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit issues or pull requests to improve the landing page.

---

**Note**: This landing page is designed to showcase ECG system design concepts and serve as an educational resource for biomedical engineering students and professionals.
