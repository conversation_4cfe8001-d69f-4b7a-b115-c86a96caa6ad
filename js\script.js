// ECG System Landing Page JavaScript

// Global Variables
let currentLanguage = 'ar';
let ecgAnimationId;
let isScrolling = false;

// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// Initialize Application
function initializeApp() {
    setupNavigation();
    setupScrollEffects();
    setupECGAnimation();
    setupLoadingScreen();
    setupSmoothScrolling();
    setupEnhancedIntersectionObserver();
    setupDiagramModal();
    setupContactForm();
}

// Navigation Setup
function setupNavigation() {
    const hamburger = document.getElementById('hamburger');
    const navMenu = document.getElementById('nav-menu');
    const navbar = document.getElementById('navbar');

    // Mobile menu toggle
    hamburger.addEventListener('click', function() {
        hamburger.classList.toggle('active');
        navMenu.classList.toggle('active');
    });

    // Close mobile menu when clicking on links
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', function() {
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
        });
    });

    // Navbar scroll effect
    window.addEventListener('scroll', function() {
        if (window.scrollY > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });
}

// Scroll Effects
function setupScrollEffects() {
    // Parallax effect for hero section
    window.addEventListener('scroll', function() {
        if (!isScrolling) {
            requestAnimationFrame(function() {
                const scrolled = window.pageYOffset;
                const hero = document.querySelector('.hero');
                if (hero) {
                    hero.style.transform = `translateY(${scrolled * 0.5}px)`;
                }
                isScrolling = false;
            });
            isScrolling = true;
        }
    });
}

// ECG Animation
function setupECGAnimation() {
    const canvas = document.getElementById('ecgCanvas');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    const width = canvas.width;
    const height = canvas.height;

    let time = 0;
    let heartRate = 75; // BPM

    function drawECG() {
        // Clear canvas
        ctx.clearRect(0, 0, width, height);

        // Draw grid
        drawGrid(ctx, width, height);

        // Draw ECG waveform
        drawECGWaveform(ctx, width, height, time, heartRate);

        time += 0.02;
        ecgAnimationId = requestAnimationFrame(drawECG);
    }

    function drawGrid(ctx, width, height) {
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.2)';
        ctx.lineWidth = 0.5;

        // Vertical lines
        for (let x = 0; x <= width; x += 20) {
            ctx.beginPath();
            ctx.moveTo(x, 0);
            ctx.lineTo(x, height);
            ctx.stroke();
        }

        // Horizontal lines
        for (let y = 0; y <= height; y += 20) {
            ctx.beginPath();
            ctx.moveTo(0, y);
            ctx.lineTo(width, y);
            ctx.stroke();
        }
    }

    function drawECGWaveform(ctx, width, height, time, heartRate) {
        ctx.strokeStyle = '#00ff88';
        ctx.lineWidth = 3;
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';

        const centerY = height / 2;
        const amplitude = height * 0.3;
        const frequency = heartRate / 60; // Hz

        ctx.beginPath();

        for (let x = 0; x < width; x++) {
            const t = (x / width) * 4 + time;
            let y = centerY;

            // Generate ECG-like waveform
            const heartCycle = (t * frequency) % 1;

            if (heartCycle < 0.1) {
                // P wave
                y += amplitude * 0.2 * Math.sin(heartCycle * Math.PI / 0.1);
            } else if (heartCycle < 0.2) {
                // PR segment
                y += 0;
            } else if (heartCycle < 0.25) {
                // Q wave
                y -= amplitude * 0.3 * Math.sin((heartCycle - 0.2) * Math.PI / 0.05);
            } else if (heartCycle < 0.3) {
                // R wave
                y += amplitude * Math.sin((heartCycle - 0.25) * Math.PI / 0.05);
            } else if (heartCycle < 0.35) {
                // S wave
                y -= amplitude * 0.4 * Math.sin((heartCycle - 0.3) * Math.PI / 0.05);
            } else if (heartCycle < 0.5) {
                // ST segment
                y += 0;
            } else if (heartCycle < 0.7) {
                // T wave
                y += amplitude * 0.3 * Math.sin((heartCycle - 0.5) * Math.PI / 0.2);
            }

            if (x === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        }

        ctx.stroke();

        // Add glow effect
        ctx.shadowColor = '#00ff88';
        ctx.shadowBlur = 10;
        ctx.stroke();
        ctx.shadowBlur = 0;
    }

    // Start animation
    drawECG();
}

// Loading Screen
function setupLoadingScreen() {
    const loading = document.getElementById('loading');

    // Show loading screen
    loading.classList.add('show');

    // Hide loading screen after content loads
    window.addEventListener('load', function() {
        setTimeout(function() {
            loading.classList.remove('show');
        }, 1000);
    });
}

// Smooth Scrolling
function setupSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                const offsetTop = target.offsetTop - 70; // Account for fixed navbar
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// Intersection Observer for Animations
function setupIntersectionObserver() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // Observe elements for animation
    document.querySelectorAll('.feature-card, .feature-item, .diagram-node').forEach(el => {
        observer.observe(el);
    });
}

// Language Toggle
function toggleLanguage() {
    currentLanguage = currentLanguage === 'ar' ? 'en' : 'ar';

    const html = document.documentElement;
    const langToggle = document.querySelector('.lang-toggle');

    if (currentLanguage === 'en') {
        html.setAttribute('lang', 'en');
        html.setAttribute('dir', 'ltr');
        langToggle.innerHTML = '<i class="fas fa-globe"></i> AR';
        updateContentToEnglish();
    } else {
        html.setAttribute('lang', 'ar');
        html.setAttribute('dir', 'rtl');
        langToggle.innerHTML = '<i class="fas fa-globe"></i> EN';
        updateContentToArabic();
    }
}

// Update Content to English
function updateContentToEnglish() {
    const translations = {
        'الرئيسية': 'Home',
        'حول النظام': 'About',
        'المميزات': 'Features',
        'المخططات': 'Diagrams',
        'التطبيقات': 'Applications',
        'اتصل بنا': 'Contact',
        'نظام تخطيط القلب الكهربائي': 'ECG System Design',
        'للتصميم والتحليل المتقدم': 'for Advanced Design and Analysis',
        'نظام شامل لتصميم وتحليل إشارات تخطيط القلب الكهربائي باستخدام أحدث تقنيات الهندسة الطبية الحيوية ومعالجة الإشارات الرقمية': 'Comprehensive system for designing and analyzing ECG signals using the latest biomedical engineering and digital signal processing techniques',
        'ابدأ الاستكشاف': 'Start Exploring',
        'عرض المخططات': 'View Diagrams',
        'فصل تعليمي': 'Educational Chapters',
        'مخطط تقني': 'Technical Diagrams',
        'تمرين عملي': 'Practical Exercises'
    };

    // Update navigation
    document.querySelectorAll('.nav-link').forEach((link, index) => {
        const arabicTexts = ['الرئيسية', 'حول النظام', 'المميزات', 'المخططات', 'التطبيقات', 'اتصل بنا'];
        const englishTexts = ['Home', 'About', 'Features', 'Diagrams', 'Applications', 'Contact'];
        if (index < englishTexts.length) {
            link.textContent = englishTexts[index];
        }
    });

    // Update hero section
    const heroTitle = document.querySelector('.hero-title');
    if (heroTitle) {
        heroTitle.innerHTML = '<span class="highlight">ECG System Design</span><br>for Advanced Analysis';
    }

    const heroDescription = document.querySelector('.hero-description');
    if (heroDescription) {
        heroDescription.textContent = 'Comprehensive system for designing and analyzing ECG signals using the latest biomedical engineering and digital signal processing techniques';
    }

    // Update buttons
    const buttons = document.querySelectorAll('.btn');
    buttons[0].innerHTML = '<i class="fas fa-play"></i> Start Exploring';
    buttons[1].innerHTML = '<i class="fas fa-chart-line"></i> View Diagrams';

    // Update stats
    const statLabels = document.querySelectorAll('.stat-label');
    statLabels[0].textContent = 'Educational Chapters';
    statLabels[1].textContent = 'Technical Diagrams';
    statLabels[2].textContent = 'Practical Exercises';
}

// Update Content to Arabic
function updateContentToArabic() {
    // Reset to original Arabic content
    location.reload(); // Simple approach - reload page to reset to Arabic
}

// Utility Functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// Performance optimized scroll handler
const optimizedScrollHandler = throttle(function() {
    // Handle scroll events here
}, 16); // ~60fps

window.addEventListener('scroll', optimizedScrollHandler);

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (ecgAnimationId) {
        cancelAnimationFrame(ecgAnimationId);
    }
});

// Error handling
window.addEventListener('error', function(e) {
    console.error('JavaScript Error:', e.error);
});

// Touch events for mobile
let touchStartY = 0;
let touchEndY = 0;

document.addEventListener('touchstart', function(e) {
    touchStartY = e.changedTouches[0].screenY;
});

document.addEventListener('touchend', function(e) {
    touchEndY = e.changedTouches[0].screenY;
    handleSwipe();
});

function handleSwipe() {
    const swipeThreshold = 50;
    const diff = touchStartY - touchEndY;

    if (Math.abs(diff) > swipeThreshold) {
        if (diff > 0) {
            // Swipe up
            console.log('Swipe up detected');
        } else {
            // Swipe down
            console.log('Swipe down detected');
        }
    }
}

// Keyboard navigation
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        // Close mobile menu if open
        const hamburger = document.getElementById('hamburger');
        const navMenu = document.getElementById('nav-menu');
        hamburger.classList.remove('active');
        navMenu.classList.remove('active');
    }
});

// Diagram Modal Functionality
function setupDiagramModal() {
    const modal = document.getElementById('diagramModal');
    const modalTitle = document.getElementById('modalTitle');
    const modalImage = document.getElementById('modalImage');
    const modalDescription = document.getElementById('modalDescription');
    const closeBtn = document.querySelector('.close');

    // Diagram data
    const diagramData = {
        'instrumentation-amplifier': {
            title: 'مكبر القياس',
            description: 'مكبر القياس هو دائرة إلكترونية متخصصة تستخدم لتكبير الإشارات الضعيفة مع رفض الضوضاء المشتركة. يتميز بمقاومة دخل عالية جداً ونسبة رفض عالية للوضع المشترك (CMRR)، مما يجعله مثالياً لتطبيقات القياس الطبي الحيوي.'
        },
        'digital-filter': {
            title: 'المرشح الرقمي',
            description: 'المرشحات الرقمية تستخدم لإزالة الضوضاء وتحسين جودة الإشارات الطبية. تشمل مرشحات تمرير منخفض، عالي، ونطاق محدد لفصل الترددات المرغوبة عن غير المرغوبة في إشارات ECG.'
        },
        'arduino-data-acquisition': {
            title: 'نظام حصول البيانات',
            description: 'نظام متكامل لحصول البيانات باستخدام Arduino يتضمن المستشعرات، المكبرات، المرشحات، ومحول ADC لتحويل الإشارات التناظرية إلى رقمية للمعالجة والتحليل.'
        },
        'realtime-system': {
            title: 'نظام الوقت الفعلي',
            description: 'نظام معالجة الإشارات في الوقت الفعلي يوفر مراقبة مستمرة وتحليل فوري لإشارات ECG مع إمكانية إرسال تنبيهات فورية عند اكتشاف أنماط غير طبيعية.'
        },
        'remote-monitoring': {
            title: 'نظام المراقبة عن بعد',
            description: 'نظام مراقبة المريض عن بعد يستخدم تقنيات IoT والاتصال اللاسلكي لنقل البيانات الطبية إلى مراكز المراقبة والأطباء المختصين.'
        },
        'driven-right-leg': {
            title: 'دائرة الساق اليمنى المدفوعة',
            description: 'دائرة متخصصة لتقليل الضوضاء في أنظمة ECG عن طريق توفير مرجع نشط يقلل من تأثير التداخل الكهربائي والضوضاء المشتركة.'
        },
        'inverting-amplifier': {
            title: 'المكبر العاكس',
            description: 'دائرة مكبر عاكس تستخدم في معالجة الإشارات الطبية لتكبير الإشارة مع عكس قطبيتها. يوفر تحكماً دقيقاً في معامل التكبير ومقاومة دخل محددة.'
        },
        'non-inverting-amplifier': {
            title: 'المكبر غير العاكس',
            description: 'دائرة مكبر غير عاكس بمقاومة دخل عالية جداً، مثالية لتكبير الإشارات الضعيفة دون تحميل المصدر. يحافظ على قطبية الإشارة الأصلية.'
        },
        'voltage-follower': {
            title: 'متابع الجهد',
            description: 'دائرة عزل تستخدم لفصل مراحل الدائرة المختلفة وتوفير مقاومة دخل عالية ومقاومة خرج منخفضة، مما يمنع تحميل الإشارة.'
        },
        'r2r-dac': {
            title: 'محول R-2R DAC',
            description: 'دائرة محول رقمي إلى تناظري باستخدام شبكة مقاومات R-2R لتحويل الإشارات الرقمية إلى إشارات تناظرية بدقة عالية.'
        },
        'lm35-arduino': {
            title: 'مستشعر LM35 مع Arduino',
            description: 'دائرة مستشعر درجة الحرارة LM35 متصل بـ Arduino لقياس درجة الحرارة في التطبيقات الطبية والمراقبة البيئية.'
        }
    };

    // Add click event to diagram items
    document.querySelectorAll('.diagram-item').forEach(item => {
        item.addEventListener('click', function() {
            const diagramType = this.getAttribute('data-diagram');
            const data = diagramData[diagramType];

            if (data) {
                modalTitle.textContent = data.title;
                modalImage.src = this.querySelector('img').src;
                modalImage.alt = data.title;
                modalDescription.textContent = data.description;
                modal.style.display = 'block';
                document.body.style.overflow = 'hidden';
            }
        });
    });

    // Close modal events
    closeBtn.addEventListener('click', closeModal);
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal();
        }
    });

    function closeModal() {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }

    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && modal.style.display === 'block') {
            closeModal();
        }
    });
}

// Contact Form Functionality
function setupContactForm() {
    const form = document.getElementById('contactForm');

    form.addEventListener('submit', function(e) {
        e.preventDefault();

        // Get form data
        const formData = new FormData(form);
        const data = {
            name: formData.get('name'),
            email: formData.get('email'),
            subject: formData.get('subject'),
            message: formData.get('message')
        };

        // Show loading
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...';
        submitBtn.disabled = true;

        // Simulate form submission (replace with actual API call)
        setTimeout(() => {
            // Reset form
            form.reset();

            // Show success message
            showNotification('تم إرسال الرسالة بنجاح!', 'success');

            // Reset button
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 2000);
    });
}

// Notification System
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;

    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#27ae60' : '#3498db'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        z-index: 3000;
        animation: slideInRight 0.3s ease-out;
    `;

    document.body.appendChild(notification);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease-out';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Enhanced Intersection Observer
function setupEnhancedIntersectionObserver() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');

                // Add staggered animation for grid items
                if (entry.target.classList.contains('diagram-item') ||
                    entry.target.classList.contains('application-card')) {
                    const siblings = Array.from(entry.target.parentElement.children);
                    const index = siblings.indexOf(entry.target);
                    entry.target.style.animationDelay = `${index * 0.1}s`;
                }
            }
        });
    }, observerOptions);

    // Observe elements for animation
    document.querySelectorAll(`
        .feature-card,
        .feature-item,
        .diagram-node,
        .diagram-item,
        .application-card,
        .contact-item,
        .footer-section
    `).forEach(el => {
        observer.observe(el);
    });
}

// Animation classes for CSS
const style = document.createElement('style');
style.textContent = `
    .animate-in {
        animation: slideInUp 0.6s ease-out forwards;
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(100px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @keyframes slideOutRight {
        from {
            opacity: 1;
            transform: translateX(0);
        }
        to {
            opacity: 0;
            transform: translateX(100px);
        }
    }

    .feature-card,
    .feature-item,
    .diagram-node,
    .diagram-item,
    .application-card,
    .contact-item,
    .footer-section {
        opacity: 0;
        transform: translateY(30px);
        transition: all 0.6s ease-out;
    }

    .notification-content {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
`;
document.head.appendChild(style);
